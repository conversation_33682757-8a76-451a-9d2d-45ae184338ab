using AngelwinFollowUp.Models;
using AngelwinFollowUp.Web.Filters;
using AngelwinFollowUp.Web.Models;
using AngelwinFollowUp.Web.Unity;
using Azure;
using Common.Tools;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.SqlServer.Server;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Collections.Generic;
using System.Drawing;
using System.Dynamic;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Text.RegularExpressions;
using static AngelwinFollowUp.Web.Controllers.UserController;
using static AngelwinFollowUp.Web.Unity.Common;

namespace AngelwinFollowUp.Web.Areas.AIFollowUp.Controllers
{
    [Authorizing]
    [Area("AIFollowUp")]
    public class FollowUpSummaryController : Controller
    {
        private IConfiguration Configuration { get; }

        private readonly AngelwinFollowUpDbContext db;
        public FollowUpSummaryController(IConfiguration configuration, AngelwinFollowUpDbContext _db)
        {
            Configuration = configuration;
            db = _db;
        }

        // GET: FollowUpSummaryController
        public ActionResult Index(int patientId, int templateId = 1)
        {
            var token = TokenGenerator.getToken();
            ViewBag.token = WebUtility.UrlEncode(token);
            ViewBag.formUrl = Configuration["AppSettings:AnyReportUrl"];
            ViewBag.TemplateId = templateId;
            var patient = db.ResearchPatients.Include(d => d.HospitalDept).FirstOrDefault(a => a.Id == patientId);
            return View(patient);
        }

        // GET: FollowUpSummaryController/Details/5
        public ActionResult Details(int id)
        {
            return View();
        }

        /// <summary>
        /// 获取患者的随访记录列表
        /// </summary>
        /// <param name="patientId">患者Id</param>
        /// <returns>随访记录列表</returns>
        public ActionResult GetFollowupRecords(int patientId, int templateId)
        {
            var followupRecords = (from plan in db.FollowupPlans
                                   join detail in db.FollowupTemplateDetails on plan.TemplateDetailId equals detail.Id
                                   join record in db.FollowupRecords on plan.Id equals record.FollowupPlanId into records
                                   from re in records.DefaultIfEmpty()
                                   where plan.PatientId == patientId && detail.TemplateId == templateId
                                   && plan.Status != -1
                                   orderby detail.Orderby
                                   select new
                                   {
                                       plan.Id,
                                       plan.TemplateDetailId,
                                       plan.PatientId,
                                       plan.Description,
                                       plan.Status,
                                       plan.StatusTime,
                                       detail.TemplateDetailName,
                                       detail.FollowupPoint,
                                       detail.Orderby,
                                       FollowupDate = plan.StatusTime.HasValue ? plan.StatusTime.Value.ToString("yyyy-MM-dd") : "",
                                       RecordId = re != null ? re.Id : 0
                                   }).ToList();

            return Json(followupRecords);
        }

        /// <summary>
        /// 随访小结
        /// </summary>
        /// <param name="followupRecordIds"></param>
        /// <returns></returns>
        [SSE]
        public async Task<IActionResult> GenerateSummary(List<int> followupRecordIds)
        {
            try
            {
                // 获取所有选中随访记录的对话内容
                var allDialogues = new List<string>();
                foreach (var recordId in followupRecordIds)
                {
                    var recordDetails = db.FollowupRecordDetails
                        .Where(o => o.FollowupRecordId == recordId)
                        .OrderBy(o => o.CreatedTime)
                        .ToList();

                    if (recordDetails.Any())
                    {
                        var dialogue = new StringBuilder();
                        dialogue.AppendLine($"第{followupRecordIds.IndexOf(recordId) + 1}次随访记录：");
                        foreach (var detail in recordDetails)
                        {
                            string role = detail.CreateUserName == "assistant" ? "医生" : "患者";
                            dialogue.AppendLine($"{role}：{detail.Content}");
                        }

                        allDialogues.Add(dialogue.ToString());
                    }
                }

                if (!allDialogues.Any())
                {
                    await Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { success = false, message = "未找到随访记录内容" })}\n\n");
                    return new EmptyResult();
                }

                // 构建提示词
                string promptTemplate = @"
        作为一名专业的医生，请根据以下随访记录生成一份完整的随访总结。
        随访记录包括：{0}
        
        请按照以下格式输出：
        1. 患者主诉：概括患者的主要症状和不适
        2. 当前状况：总结患者目前的整体情况
        3. 存在的问题：列出需要关注的健康问题
        4. 医生建议：给出专业的建议和指导
        
        请确保总结简明扼要，突出重点，并体现患者病情的变化过程。";

                string prompt = string.Format(promptTemplate, string.Join("\n\n", allDialogues));

                // 设置响应头
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await GenerateAISummary(prompt);
                return new EmptyResult();
            }
            catch (Exception ex)
            {
                await Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { success = false, message = ex.Message })}\n\n");
                return new EmptyResult();
            }
        }

        private async Task GenerateAISummary(string prompt)
        {
            LoggerHelper.WriteInfo("其他日志", $"GenerateAISummary:开始");
            try
            {
                // 从配置获取API设置
                string modelType = Configuration["AppSettings:modelType"] ?? string.Empty;
                string apiUrl = Configuration[$"GPTSetting:{modelType}:ApiUrl"] ?? string.Empty;
                string apiKey = Configuration[$"GPTSetting:{modelType}:ApiKey"] ?? string.Empty;
                string model = Configuration[$"GPTSetting:{modelType}:Model"] ?? string.Empty;

                // 验证关键字
                var filterKeywordList = CheckKeyWords(prompt);
                if (filterKeywordList.Any())
                {
                    throw new InvalidOperationException($"含有敏感信息：{string.Join(",", filterKeywordList.ToArray())}");
                }

                using (var httpClient = new HttpClient())
                {
                    httpClient.BaseAddress = new Uri(apiUrl);
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                    var requestBody = JsonConvert.SerializeObject(new
                    {
                        model,
                        messages = new[]
                        {
                    new { role = "user", content = prompt }
                },
                        stream = true
                    });

                    var request = new HttpRequestMessage(HttpMethod.Post, "chat/completions")
                    {
                        Content = new StringContent(requestBody, Encoding.UTF8, "application/json")
                    };

                    //var summaryResult = new SummaryResult();
                    var currentSection = string.Empty;
                    var sectionContent = new StringBuilder();
                    var summaryResult = new Dictionary<string, string>();
                    var responseContent = new StringBuilder();
                    using (var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead))
                    {
                        if (!response.IsSuccessStatusCode)
                        {
                            var errorMsg = "Failed to connect to API.";
                            await Response.WriteAsync($"event: end\ndata:{JsonConvert.SerializeObject(new { errorMsg = errorMsg })}\n\n");
                        }

                        using (var stream = await response.Content.ReadAsStreamAsync())
                        using (var reader = new StreamReader(stream))
                        {

                            while (!reader.EndOfStream)
                            {
                                var line = await reader.ReadLineAsync();
                                if (string.IsNullOrEmpty(line)) continue;
                                if (!line.StartsWith("data: ")) continue;

                                var data = line.Substring(6);
                                try
                                {
                                    var json = JObject.Parse(data);
                                    var content = json.SelectToken("choices[0].delta.content")?.ToString();
                                    if (data != "[DONE]")
                                    {
                                        if (!string.IsNullOrEmpty(content))
                                        {
                                            responseContent.Append(content);
                                            await Response.WriteAsync($"data:{JsonConvert.SerializeObject(new { okMsg = "成功", success = true, data = content })}\n\n");
                                            await Response.Body.FlushAsync();
                                        }
                                    }
                                }
                                catch (JsonReaderException)
                                {
                                    continue;
                                }
                            }
                        }
                    }
                    LoggerHelper.WriteInfo("其他日志", $"GenerateAISummary:结束");
                    var resultAll = new
                    {
                        okMsg = $"成功",
                        success = true,
                        section = currentSection,
                        role = "assistant",
                        content = responseContent.ToString()
                    };
                    await Response.WriteAsync($"event: end\ndata: {JsonConvert.SerializeObject(resultAll)}\n\n");
                    await Response.Body.FlushAsync();
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteInfo("其他日志", $"GenerateAISummary错误: {ex.Message}");
                await Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { success = false, message = ex.Message })}\n\n");
            }
        }

        //AI报告提取
        [HttpPost]
        public async Task<IActionResult> NewAIExtract(int patientId, List<int> followupRecordIds)
        {
            try
            {
                //var CRFForm = db.CRForms.Find(CRFormId);
                //var commAPI = new CommAPIController(db, webhostEnv, config);
                string modelType = Configuration["AppSettings:modelType"] ?? string.Empty;
                var tips = "";
                var data = string.Empty;
                string Newprompt = string.Empty;
                #region 获取通用提示词逻辑（注释）
                //if (string.IsNullOrWhiteSpace(tips))
                //{

                //    //var TipsResult = commAPI.GetPromptTips(CRFForm.FormId, "CollectDataPage", modelType);
                //    //var jsonResult = TipsResult as JsonResult;
                //    //if (jsonResult != null)
                //    //{
                //    //    // 将JsonResult的Value对象序列化为字符串
                //    //    string jsonString = JsonConvert.SerializeObject(jsonResult.Value);
                //    //    // 反序列化字符串为匿名对象
                //    //    var dataObject = JsonConvert.DeserializeObject<dynamic>(jsonString);
                //    //    // 获取data属性的值
                //    //    tips = dataObject.data;
                //    //    var code = dataObject.code;
                //    //    if (code != 0)
                //    //    {
                //    //        return Json(new { code = -100, msg = "获取提示词出错！", tips = "" });
                //    //    }
                //    //}
                //}
                #endregion
                if (patientId == 1001)
                {
                    tips = @"<角色>
                        角色：你是一个专业的随访医生，精通于分析随访记录。
                        </角色> 
                        <任务> 
                        任务：
                        根据用户的输入医生和患者的随访记录问答结果，准确提取指定特征变量，并按照规定的格式输出。 
                        </任务> 
                        <背景> 
                        背景：用户提供了包含随访问答记录的数据文本，并要求从中提取特定的特征变量。 
                        </背景> 
                        <执行要求> 
                        执行要求： 
                        1、仔细分析用户提供的文本，确保理解其内容和要求。 
                        2、如果文本中信息不明确或不足以完成任务，需向用户澄清。
                        3、 严格按照用户指定的格式输出结果。 
                        4、确保输出内容与用户提供的文本描述一致。 
                        5、使用与用户问题相同的语言进行回答。 
                        6、日期格式统一采用“yyyy-MM-dd”。 
                        </执行要求> 
                        <输出要求> 
                        输出要求： 按照用户指定的输出格式串进行输出。 确保提取的特征变量准确无误。 保持回答的逻辑性和结构清晰。 
                        </输出要求> 
                        <输入> 
                        用户输入：使用<data></data>标记中的内容作为你的知识:<data>{{随访内容}}</data>
                        回答要求： 如果你不清楚答案，你需要澄清。 避免提及你是从获取的知识。 保持答案与中描述的一致。
                            从报告中提取以下特征变量：
                                    {
	                                    ""说明"": ""从随访记录中提取以下特征变量。"",
	                                    ""特征提取"": [{
		                                    ""变量名"": ""FOLLOW_UP_PERSON"",
		                                    ""变量描述"": ""随访人"",
		                                    ""提取要求"": ""只提取患者姓名""
	                                    }, {
		                                    ""变量名"": ""FOLLOW_UP_TIME"",
		                                    ""变量描述"": ""随访时间"",
		                                    ""提取要求"": """"
	                                    }, {
		                                    ""变量名"": ""FOLLOWUP_METHOD"",
		                                    ""变量描述"": ""随访方式"",
		                                    ""提取要求"": ""固定值：网络""
	                                    }, {
		                                    ""变量名"": ""DIAGNOSIS"",
		                                    ""变量描述"": ""诊断"",
		                                    ""提取要求"": """"
	                                    }, {
		                                    ""变量名"": ""GI_SYMPTOMS"",
		                                    ""变量描述"": ""消化道症状"",
		                                    ""提取要求"": ""从随访记录中提取是否有以下消化道症状“便血,腹泻,便秘,呕吐,腹痛腹胀,肠梗阻”，如果有多个消化道症状，使用&&&###拼接。""
	                                    }, {
		                                    ""变量名"": ""SYSTEMIC_SYMPTOMS"",
		                                    ""变量描述"": ""全身症状"",
		                                    ""提取要求"": ""从随访记录中提取是否有以下全身症状“体重下降,乏力,食欲减退,夜间盗汗”，如果有多个全身症状，使用&&&###拼接。"",
		                                    ""原文依据"": ""记录描述文字""
	                                    }, {
		                                    ""变量名"": ""METASTATIC_SYMPTOMS"",
		                                    ""变量描述"": ""转移相关症状"",
		                                    ""提取要求"": ""从随访记录中提取是否有以下转移相关症状“咳嗽,胸痛,呼吸困难,黄疸,骨痛”，如果有多个转移相关症状，使用&&&###拼接。""
	                                    }, {
		                                    ""变量名"": ""POSTOP_STATUS"",
		                                    ""变量描述"": ""术后恢复情况"",
		                                    ""提取要求"": ""根据术后恢复相关问题总结""
	                                    }, {
		                                    ""变量名"": ""MEDICATION_INFO"",
		                                    ""变量描述"": ""服药情况"",
		                                    ""提取要求"": ""根据服药情况相关问题总结""
	                                    }, {
		                                    ""变量名"": ""TREATMENT_INFO"",
		                                    ""变量描述"": ""治疗情况"",
		                                    ""提取要求"": ""根据治疗情况相关问题总结""
	                                    }, {
		                                    ""变量名"": ""RECENT_EXAM"",
		                                    ""变量描述"": ""近期检查情况"",
		                                    ""提取要求"": ""根据近期检查情况相关问题总结""
	                                    }, {
		                                    ""变量名"": ""DIET_STATUS"",
		                                    ""变量描述"": ""饮食情况"",
		                                    ""提取要求"": ""根据饮食情况相关问题总结""
	                                    }, {
		                                    ""变量名"": ""EXERCISE_STATUS"",
		                                    ""变量描述"": ""运动情况"",
		                                    ""提取要求"": ""根据运动情况相关问题总结""
	                                    }, {
		                                    ""变量名"": ""MENTAL_STATUS"",
		                                    ""变量描述"": ""心理情况"",
		                                    ""提取要求"": ""根据心理情况相关问题总结""
	                                    }]
                                    }
                            输出格式串要求如下：{""variables"":[{""variable_name"":"""",""变量描述"":"""",""value"":""""}]} 
                        </输入>";
                }
                else if (patientId == 1003) {
                    tips = @"<角色>
                             角色：你是一个专业的随访医生，精通于分析随访记录。
                            </角色>
                             <任务>
                             任务：
                             根据用户的输入医生和患者的随访记录问答结果，准确提取指定特征变量，并按照规定的格式输出。
                             </任务>
                             <背景>
                             背景：用户提供了包含随访问答记录的数据文本，并要求从中提取特定的特征变量。
                             </背景>
                             <执行要求>
                             执行要求：
                             1、仔细分析用户提供的文本，确保理解其内容和要求。
                             2、如果文本中信息不明确或不足以完成任务，需向用户澄清。
                             3、 严格按照用户指定的格式输出结果。
                             4、确保输出内容与用户提供的文本描述一致。
                             5、使用与用户问题相同的语言进行回答。
                             6、日期格式统一采用""yyyy-MM-dd""。
                             </执行要求>
                             <输出要求>
                             输出要求： 按照用户指定的输出格式串进行输出。 确保提取的特征变量准确无误。 保持回答的逻辑性和结构清晰。
                             </输出要求>
                             <输入>
                             用户输入：使用<data></data>标记中的内容作为你的知识:<data>{{随访内容}}</data>
                             回答要求： 如果你不清楚答案，你需要澄清。 避免提及你是从获取的知识。 保持答案与中描述的一致。
                            --从评估量表记录中提取以下特征变量：
                            {
                              ""说明"": ""从报告中提取以下特征变量。"",
                              ""特征提取"": [
                                {
                                  ""变量名"": ""PATIENT_NAME_INPUT"",
                                  ""变量描述"": ""患者姓名"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_TRIVASTAL_DOSAGE"",
                                  ""变量描述"": ""用药剂量"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_TRIVASTAL_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前or饭后用药"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_TRIVASTAL_TIMES_PER_DAY"",
                                  ""变量描述"": ""用药次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_PRAMIPEXOLE_ER_HOURS_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前多少h服用药物"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_PRAMIPEXOLE_ER_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前or饭后用药"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_PRAMIPEXOLE_ER_DOSAGE"",
                                  ""变量描述"": ""用药剂量"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_PRAMIPEXOLE_ER_TIMES_PER_DAY"",
                                  ""变量描述"": ""用药次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_PRAMIPEXOLE_ER_TYPE"",
                                  ""变量描述"": ""国产or进口"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_PRAMIPEXOLE_HOURS_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前多少h服用药物"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_PRAMIPEXOLE_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前or饭后用药"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_PRAMIPEXOLE_DOSAGE"",
                                  ""变量描述"": ""用药剂量"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_PRAMIPEXOLE_TIMES_PER_DAY"",
                                  ""变量描述"": ""用药次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_PRAMIPEXOLE_TYPE"",
                                  ""变量描述"": ""国产or进口"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_WITH_LEVODOPA_YES"",
                                  ""变量描述"": ""是否跟左旋多巴一起吃"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_ENTACAPONE_HOURS_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前多少h服用药物"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_ENTACAPONE_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前or饭后用药"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_ENTACAPONE_DOSAGE"",
                                  ""变量描述"": ""用药剂量"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_ENTACAPONE_TIMES_PER_DAY"",
                                  ""变量描述"": ""用药次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_SILEME_HOURS_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前多少h服用药物"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_SILEME_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前or饭后用药"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_SILEME_DOSAGE"",
                                  ""变量描述"": ""用药剂量"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_SILEME_TIMES_PER_DAY"",
                                  ""变量描述"": ""用药次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_SINEMET_HOURS_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前多少h服用药物"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_REQUIP_TIMES_PER_DAY"",
                                  ""变量描述"": ""用药次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_SINEMET_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前or饭后用药"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_REQUIP_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前or饭后用药"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_NEUPRO_TIMES_PER_DAY"",
                                  ""变量描述"": ""用药次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""SPECIAL_DISCOMFORT_DESC"",
                                  ""变量描述"": ""你特别需要医生解决的不舒服是什么"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""WEARING_OFF_TIME"",
                                  ""变量描述"": ""服药后多久好转"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""WEARING_OFF_YES"",
                                  ""变量描述"": ""是否会觉得药物越来越没有效果"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DYSKINESIA_ONSET_TIME"",
                                  ""变量描述"": ""服药后多久好转"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DYSKINESIA_YES"",
                                  ""变量描述"": ""服药后会不会出现身体扭动的表现"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""SYMPTOM_FLUCTUATION_YES"",
                                  ""变量描述"": ""情况会不会"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""POST_DOSE_RELIEF_TIME"",
                                  ""变量描述"": ""服药后多久好转"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""POST_DOSE_TREMOR"",
                                  ""变量描述"": ""在服药后四小时左右或者下一餐服药之前会不会觉得"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DRUG_EFFECTIVE_YES"",
                                  ""变量描述"": ""抗帕金森药物有没有效果"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""OTHER_DRUG_NAME"",
                                  ""变量描述"": ""其他药物"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DRUG_AMANTADINE_DOSE"",
                                  ""变量描述"": ""每次用药多少片"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DRUG_AMANTADINE_FREQ"",
                                  ""变量描述"": ""用药次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DRUG_TRIHEXYPHENIDYL_DOSE"",
                                  ""变量描述"": ""每次用药多少片"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DRUG_TRIHEXYPHENIDYL_FREQ"",
                                  ""变量描述"": ""用药次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DRUG_RASAGILINE_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前or饭后"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DRUG_RASAGILINE_DOSE"",
                                  ""变量描述"": ""每次用药多少片"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DRUG_RASAGILINE_FREQ"",
                                  ""变量描述"": ""用药次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DRUG_SELEGILINE_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前or饭后"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DRUG_SELEGILINE_DOSE"",
                                  ""变量描述"": ""每次用药多少片"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DRUG_SELEGILINE_FREQ"",
                                  ""变量描述"": ""用药次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_NEUPRO_CHANGE_TIME"",
                                  ""变量描述"": ""更换时间"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_NEUPRO_DOSAGE"",
                                  ""变量描述"": ""用药剂量"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_NEUPRO_ONCE"",
                                  ""变量描述"": ""每次用药多少片"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_REQUIP_DOSAGE"",
                                  ""变量描述"": ""用药剂量"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_SINEMET_DOSAGE"",
                                  ""变量描述"": ""用药剂量"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_SINEMET_TIMES_PER_DAY"",
                                  ""变量描述"": ""用药次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_MADOPAR_HOURS_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前多少h服用药物"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""CONSTIPATION_FREQ"",
                                  ""变量描述"": ""多少天一次大便"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""CONSTIPATION_STATUS"",
                                  ""变量描述"": ""有没有便秘"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""START_CROWD"",
                                  ""变量描述"": ""人多的时候"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""START_NARROW"",
                                  ""变量描述"": ""人多经过狭小的空间的时候的时候："",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""START_INITIAL"",
                                  ""变量描述"": ""尤其是在刚刚准备走的时候"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""START_DIFFICULTY"",
                                  ""变量描述"": ""脚好像被黏住了，迈步困难"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""FALL_COUNT"",
                                  ""变量描述"": ""摔倒次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""POSTURE_PROBLEM"",
                                  ""变量描述"": ""姿势不稳"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""TREMOR_WORSE_SIDE"",
                                  ""变量描述"": ""现在哪一侧最严重"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""TREMOR_START_SIDE"",
                                  ""变量描述"": ""哪一侧先开始"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""TREMOR_HOLDING"",
                                  ""变量描述"": ""抖动"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""TREMOR_FOOT"",
                                  ""变量描述"": ""脚抖"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""TREMOR_HAND"",
                                  ""变量描述"": ""手抖"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""STIFFNESS_WORST"",
                                  ""变量描述"": ""哪一侧最严重？"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""TURNING_SIDE"",
                                  ""变量描述"": ""躺在床上翻身困难"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""STIFF_FINGER"",
                                  ""变量描述"": ""手指僵硬"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""STIFF_KNEE"",
                                  ""变量描述"": ""膝关节僵硬"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""STIFF_SHOULDER"",
                                  ""变量描述"": ""肩关节僵硬"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""SLOW_MOVEMENT_QT"",
                                  ""变量描述"": ""其他"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""SLOW_MOVEMENT"",
                                  ""变量描述"": ""动作变慢"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DIAGNOSIS_PD"",
                                  ""变量描述"": ""是否曾经被诊断为帕金森病"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""INSURANCE_CARD_INPUT"",
                                  ""变量描述"": ""医保卡号"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""ADMISSION_NUMBER_INPUT"",
                                  ""变量描述"": ""住院号"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""CONSTIPATION_NOTE"",
                                  ""变量描述"": ""多少年了"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""OLFACTORY_LOSS"",
                                  ""变量描述"": ""鼻子有没有闻不到东西"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""OLFACTORY_YEARS"",
                                  ""变量描述"": ""鼻子闻不到东西多少年了"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""OLFACTORY_TIMING"",
                                  ""变量描述"": ""帕金森出现前后"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_MADOPAR_BEFORE_MEAL"",
                                  ""变量描述"": ""饭前or饭后用药"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MED_MADOPAR_DOSAGE"",
                                  ""变量描述"": ""用药剂量"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""CURRENT_MED_MADOPAR_DAILY_FREQ"",
                                  ""变量描述"": ""用药次数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MEDICATION_STATUS"",
                                  ""变量描述"": ""是否正在接受药物治疗"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DIAGNOSIS_DETAIL"",
                                  ""变量描述"": ""诊断什么病"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""MEDICAL_VISIT"",
                                  ""变量描述"": ""是否就诊过"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""POSTURAL_YEARS"",
                                  ""变量描述"": ""姿势不稳年数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""TREMOR_DURATION"",
                                  ""变量描述"": ""抖动年数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""RIGIDITY_YEARS"",
                                  ""变量描述"": ""具体哪一年肢体僵硬："",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""BRADYKINESIA_YEAR"",
                                  ""变量描述"": ""具体哪一年动作变慢"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""BRADYKINESIA_YEARS"",
                                  ""变量描述"": ""动作变慢"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DIABETES_YES"",
                                  ""变量描述"": ""有没有糖尿病"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""HALLUCINATION_YEARS"",
                                  ""变量描述"": ""幻觉年数"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""ANXIETY_YEARS"",
                                  ""变量描述"": ""焦虑情绪多少年"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""ANXIETY_STATUS"",
                                  ""变量描述"": ""有没有焦虑情绪"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DEPRESSION_YEARS"",
                                  ""变量描述"": ""抑郁情绪多少年"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DEPRESSION_STATUS"",
                                  ""变量描述"": ""有没有抑郁情绪"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DIZZINESS_YEARS"",
                                  ""变量描述"": ""头晕多少年了"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""DIZZINESS_STATUS"",
                                  ""变量描述"": ""有没有头晕"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""INSOMNIA_YEARS"",
                                  ""变量描述"": ""INSOMNIA_"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""INSOMNIA_STATUS"",
                                  ""变量描述"": ""有没有失眠？"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""RBD_TIMING"",
                                  ""变量描述"": ""帕金森出现前后"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""RBD_YEARS"",
                                  ""变量描述"": ""多少年了"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""RBD_SYMPTOMS"",
                                  ""变量描述"": ""睡觉的时候会不会大喊大叫，手脚乱舞，噩梦多，梦话很多"",
                                  ""提取要求"": """"
                                },
                                {
                                  ""变量名"": ""HALLUCINATION_TYPE"",
                                  ""变量描述"": ""有没有幻觉"",
                                  ""提取要求"": """"
                                }
                              ]
                            };
                               -输出格式串要求如下：
                            { \""variables\"":[{\""variable_name\"":\"""",\""value\"":\""""}]}

                            </输入>";
                }
                #region 获取随访记录
                var allDialogues = new List<string>();
                var patientInfo = db.ResearchPatients.FirstOrDefault(d => d.Id == patientId);
                allDialogues.Add($"患者名称：{patientInfo?.PatientName},患者性别：{patientInfo?.Sex},患者诊断信息:{patientInfo?.Diagnosis}");
                foreach (var recordId in followupRecordIds)
                {
                    var recordDetails = db.FollowupRecordDetails
                        .Where(o => o.FollowupRecordId == recordId)
                        .OrderBy(o => o.CreatedTime)
                        .ToList();
                    var latestFollowup = (from record in db.FollowupRecords
                                          join template in db.FollowupTemplateDetails
                                              on record.TemplateDetailId equals template.Id
                                          where record.Id == recordId
                                          orderby record.CreatedTime descending
                                          select new
                                          {
                                              template.Methods,
                                              record.CreatedTime
                                          }).FirstOrDefault();
                    //var recordDetails1 = (from detail in db.FollowupRecordDetails
                    //                     join record in db.FollowupRecords
                    //                         on detail.FollowupRecordId equals record.Id
                    //                     join template in db.FollowupTemplateDetails
                    //                         on record.TemplateDetailId equals template.Id
                    //                     where detail.FollowupRecordId == recordId
                    //                     orderby detail.CreatedTime
                    //                     select new
                    //                     {
                    //                         detail.Content,
                    //                         detail.CreateUserName,
                    //                         detail.CreatedTime,
                    //                         FollowupMethod = template.Methods
                    //                     }).ToList();

                    if (recordDetails.Any())
                    {
                        var dialogue = new StringBuilder();
                        // 获取最近一次创建时间
                        var latestCreatedTime = recordDetails.Max(x => x.CreatedTime);
                        dialogue.AppendLine($"第{followupRecordIds.IndexOf(recordId) + 1}次随访记录：");
                        // 添加填写时间标记
                        dialogue.AppendLine($"填写日期：{latestCreatedTime:yyyy-MM-dd}");
                        // 添加最近一次的随访方式
                        if (latestFollowup != null)
                        {
                            dialogue.AppendLine($"最近随访方式：网络");
                        }
                        foreach (var detail in recordDetails)
                        {
                            string role = detail.CreateUserName == "assistant" ? "医生" : "患者";
                            dialogue.AppendLine($"{role}：{detail.Content}");
                        }

                        allDialogues.Add(dialogue.ToString());
                    }
                }
                
                if (!allDialogues.Any())
                {
                    await Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { success = false, message = "未找到随访记录内容" })}\n\n");
                    return new EmptyResult();
                }
                #endregion
                Newprompt = tips.Replace("{{随访内容}}", string.Join("\n\n", allDialogues));
                LoggerHelper.WriteInfo("其他日志", $"随访AIExtract:开始");

                #region 读取配置文件
                string apiKey = Configuration[$"GPTSetting:{modelType}:ApiKey"] ?? string.Empty;
                string model = Configuration[$"GPTSetting:{modelType}:Model"] ?? string.Empty;
                string apiUrl = Configuration[$"GPTSetting:{modelType}:apiUrl"] ?? string.Empty;
                string maxTokenStr = Configuration[$"GPTSetting:{modelType}:MaxTokens"] ?? string.Empty;
                int maxTokens = 4000;
                var modelName = modelType;

                var JArrayData = new JArray();
                HttpClientHandler handler;
                string webProxy = Configuration[$"GPTSetting:{modelType}:WebProxy"] ?? string.Empty;
                if (!string.IsNullOrWhiteSpace(webProxy))
                {
                    handler = new HttpClientHandler()
                    {
                        Proxy = new WebProxy(webProxy)
                    };
                }
                else
                {
                    handler = new HttpClientHandler() { };
                }

                #endregion
                string endpoint = "chat/completions";

                dynamic history = new List<dynamic>();
                var httpClient = new HttpClient(handler);
                httpClient.Timeout = TimeSpan.FromMinutes(5);
                httpClient.BaseAddress = new Uri(apiUrl);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                history.Add(new { role = "user", content = $"{Newprompt}" });

                var requestMsgTime = System.DateTime.Now;

                dynamic requstDTO = new ExpandoObject();
                requstDTO.model = model;
                requstDTO.messages = history;
                requstDTO.stream = false;
                requstDTO.max_tokens = maxTokens;
                var requestBody = JsonConvert.SerializeObject(requstDTO);
                var request = new HttpRequestMessage(HttpMethod.Post, endpoint)
                {
                    Content = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
                };
                var ResponseContent = "";
                using var APIResponse = await httpClient.SendAsync(request);
                {
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var stream = await APIResponse.Content.ReadAsStreamAsync();
                        using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                        {
                            // ResponseContent = reader.ReadToEnd();
                            var resultAI = reader.ReadToEnd();
                            var jobj = JsonConvert.DeserializeObject<JObject>(resultAI);
                            if (jobj != null && jobj.ContainsKey("choices"))
                            {
                                var choicesList = jobj.Value<JArray>("choices");
                                var choicesItem = choicesList?.FirstOrDefault();
                                if (choicesItem != null)
                                {
                                    var content = choicesItem?.Value<JObject>("message")?.Value<string>("content");
                                    Console.WriteLine($"大模型结果:{content}");
                                    if (!string.IsNullOrEmpty(content))
                                    {
                                        LoggerHelper.WriteInfo("其他日志", $"大模型结果:{content}");
                                        var first = content.IndexOf("{");
                                        var last = content.LastIndexOf("}");
                                        ResponseContent = content.Substring(first, last - first + 1);
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        return Json(new { code = -100, msg = "Failed to connect to API！" });

                    }
                }

                return Json(new { code = 0, data = ResponseContent });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteInfo("其他日志", $"调用AI随访提取报错:{ex.Message + ex.InnerException?.Message}");
                return Json(new { code = -100, msg = ex.Message });
            }
        }

        private List<string> CheckKeyWords(string prompt)
        {
            List<string> matchedKeywords = new List<string>();
            try
            {
                var keywords = Configuration["GPTSetting:FilterKeywords"];
                var keywordArrys = keywords?.Split(',');
                if (keywordArrys != null && keywordArrys.Length > 0)
                {
                    foreach (string keyword in keywordArrys)
                    {
                        string pattern = $@"{Regex.Escape(keyword)}";
                        if (Regex.IsMatch(prompt, pattern, RegexOptions.IgnoreCase))
                        {
                            matchedKeywords.Add(keyword);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                matchedKeywords.Add(ex.Message);
            }
            return matchedKeywords;
        }

        /// <summary>
        /// 加载表单
        /// </summary>
        /// <returns></returns>
        public IActionResult LoadFormData()
        {
            try
            {
                var formData = db.FormDatas.FirstOrDefault();
                return Json(new { code = 0, msg = "操作成功！", data = TransformData(formData?.CRFJsonValue) });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteErrorLog("加载表单数据报错！", ex);
                return Json(new { code = -100, msg = "获取数据失败！" + ex.Message });
            }
        }

        public string TransformData(string jsonInput)
        {

            var inputObject = JObject.Parse(jsonInput);
            Root root = new Root { variables = new List<Variable>() };

            foreach (var property in inputObject.Properties())
            {
                JToken propertyValue = property.Value;

                string value;
                if (propertyValue.Type == JTokenType.Array)
                {
                    // 如果值是数组，将其转换为用逗号分隔的字符串
                    //   value = string.Join(",", ((JArray)propertyValue).Select(token => (string)token));
                    value = JsonConvert.SerializeObject(((JArray)propertyValue));
                }
                else
                {
                    value = (string)propertyValue;
                }

                Variable variable = new Variable
                {
                    variable_name = property.Name,
                    value = value,
                    source = ""
                };
                root.variables.Add(variable);
            }

            return JsonConvert.SerializeObject(root);
        }

        /// <summary>
        /// 保存表单
        /// </summary>
        /// <param name="CRFormId"></param>
        /// <param name="CRFJsonValue"></param>
        /// <param name="AIExtractJsonValue"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveForm(string CRFormId, string CRFJsonValue,string AIExtractJsonValue)
        {
            try
            {
                LoggerHelper.WriteInfo("其他日志", $"提交表单ID：【{CRFormId}】,表单数据：{CRFJsonValue}");

                // 如果需要遍历所有属性，可以使用JObject
                JObject jObject = JObject.Parse(CRFJsonValue);
                var fieldValueDict = new List<FillFieldDTO>();
                foreach (JProperty property in jObject.Properties())
                {
                    JToken propertyValue = property.Value;
                    var propertyName = property.Name;
                    if (!propertyName.ToLower().Contains("detailarray_"))
                    {
                        fieldValueDict.Add(new FillFieldDTO { FieldName = propertyName, FieldValue = propertyValue.ToString(), FieldComment = "" });
                    }
                    else
                    {
                        var DetailList = (JArray)property.Value;
                        foreach (JObject detail in DetailList)
                        {
                            foreach (JProperty pro in detail.Properties())
                            {
                                var proValue = pro.Value;
                                var proName = pro.Name;
                                fieldValueDict.Add(new FillFieldDTO { FieldName = proName, FieldValue = proValue.ToString(), FieldComment = "" });
                            }
                        }
                    }
                }

                var crfData = db.FormDatas.FirstOrDefault(a => a.CRFormId == CRFormId);
                if (crfData != null)
                {
                    crfData.CRFJsonValue = CRFJsonValue;
                    if (!string.IsNullOrEmpty(AIExtractJsonValue))
                        crfData.AIExtractJsonValue = AIExtractJsonValue;
                    crfData.CreatedTime = DateTime.Now;
                    db.SaveChanges();
                }
                else
                {
                    crfData = new FormData();
                    crfData.CRFormId = CRFormId;
                    crfData.CRFJsonValue = CRFJsonValue;
                    crfData.AIExtractJsonValue = AIExtractJsonValue;
                    crfData.CreatedTime = DateTime.Now;
                    crfData.CreateUserName = User.Identity.Name;
                    db.FormDatas.Add(crfData);
                    db.SaveChanges();
                }

                return Json(new { code = 0, msg = "操作成功！" });
            }
            catch (Exception ex)
            {
                LoggerHelper.WriteErrorLog("提交表单报错！", ex);
                return Json(new { code = -100, msg = "操作失败！", data = ex });
            }
        }

        /// <summary>
        /// 下载
        /// </summary>
        /// <returns></returns>
        public IActionResult DownLoad()
        {
            var query = db.FormDatas.FirstOrDefault();
            var AIExtractJsonValue = query?.AIExtractJsonValue ?? string.Empty;
            // 解析JSON数据
            var jsonData = JsonConvert.DeserializeObject<FollowupDataModel>(AIExtractJsonValue);
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            Stream stream = new MemoryStream();
            using (ExcelPackage package = new ExcelPackage(stream))
            {
                ExcelWorksheet worksheet = package.Workbook.Worksheets.Add("result");//创建worksheet

                #region 表头、样式

                // 设置列数 - 根据实际数据调整
                int columnCount = 15;

                // 设置第一行主标题并居中
                worksheet.Cells[1, 1, 1, columnCount].Merge = true;
                worksheet.Cells[1, 1].Value = "患者随访记录表";
                worksheet.Cells[1, 1, 1, columnCount].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells[1, 1, 1, columnCount].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                worksheet.Cells[1, 1, 1, columnCount].Style.Font.Bold = true;
                worksheet.Cells[1, 1, 1, columnCount].Style.Font.Size = 16;

                // 设置行高
                worksheet.Row(1).Height = 30;
                worksheet.Row(2).Height = 25;
                worksheet.Row(3).Height = 25;

                // 定义表头
                var headers = new Dictionary<int, string>
                {
                    { 1, "序号" },
                    { 2, "随访人" },
                    { 3, "随访时间" },
                    { 4, "随访方式" },
                    { 5, "诊断" },
                    { 6, "消化道症状" },
                    { 7, "全身症状" },
                    { 8, "转移相关症状" },
                    { 9, "术后恢复情况" },
                    { 10, "服药情况" },
                    { 11, "治疗情况" },
                    { 12, "近期检查情况" },
                    { 13, "饮食情况" },
                    { 14, "运动情况" },
                    { 15, "心理情况" }
                };

                // 设置第二行和第三行表头
                foreach (var header in headers)
                {
                    // 先设置第2行和第3行的每个单元格的边框
                    for (int row = 2; row <= 3; row++)
                    {
                        worksheet.Cells[row, header.Key].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        worksheet.Cells[row, header.Key].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        worksheet.Cells[row, header.Key].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        worksheet.Cells[row, header.Key].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    }

                    // 然后合并单元格并设置其他样式
                    worksheet.Cells[2, header.Key, 3, header.Key].Merge = true;
                    worksheet.Cells[2, header.Key].Value = header.Value;
                    worksheet.Cells[2, header.Key, 3, header.Key].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    worksheet.Cells[2, header.Key, 3, header.Key].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    worksheet.Cells[2, header.Key, 3, header.Key].Style.Font.Bold = true;
                    worksheet.Cells[2, header.Key, 3, header.Key].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[2, header.Key, 3, header.Key].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                }

               
                #endregion

                #region 填充数据
                // 从JSON数据中填充内容
                if (jsonData != null && jsonData.variables != null && jsonData.variables.Count > 0)
                {
                    int rowIndex = 4; // 从第4行开始填充数据

                    // 序号
                    worksheet.Cells[rowIndex, 1].Value = 1;

                    // 映射变量名到列索引
                    var variableToColumnMap = new Dictionary<string, int>
                    {
                        { "FOLLOW_UP_PERSON", 2 },
                        { "FOLLOW_UP_TIME", 3 },
                        { "FOLLOWUP_METHOD", 4 },
                        { "DIAGNOSIS", 5 },
                        { "GI_SYMPTOMS", 6 },
                        { "SYSTEMIC_SYMPTOMS", 7 },
                        { "METASTATIC_SYMPTOMS", 8 },
                        { "POSTOP_STATUS", 9 },
                        { "MEDICATION_INFO", 10 },
                        { "TREATMENT_INFO", 11 },
                        { "RECENT_EXAM", 12 },
                        { "DIET_STATUS", 13 },
                        { "EXERCISE_STATUS", 14 },
                        { "MENTAL_STATUS", 15 }
                    };

                    // 先为整行设置边框和样式
                    for (int col = 1; col <= columnCount; col++)
                    {
                        // 设置单元格样式
                        worksheet.Cells[rowIndex, col].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        worksheet.Cells[rowIndex, col].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        worksheet.Cells[rowIndex, col].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        worksheet.Cells[rowIndex, col].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        worksheet.Cells[rowIndex, col].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        worksheet.Cells[rowIndex, col].Style.VerticalAlignment = ExcelVerticalAlignment.Center;

                        // 为空单元格设置默认值，确保显示边框
                        if (col > 1 && !variableToColumnMap.ContainsValue(col))
                        {
                            worksheet.Cells[rowIndex, col].Value = "";
                        }
                    }

                    // 填充数据
                    foreach (var variable in jsonData.variables)
                    {
                        if (variableToColumnMap.ContainsKey(variable.variable_name))
                        {
                            int colIndex = variableToColumnMap[variable.variable_name];
                            worksheet.Cells[rowIndex, colIndex].Value = variable.value;
                        }
                    }
                }
                #endregion

                // 调整列宽以适应内容
                for (int col = 1; col <= columnCount; col++)
                {
                    worksheet.Column(col).AutoFit();
                    // 设置最小宽度
                    if (worksheet.Column(col).Width < 15)
                        worksheet.Column(col).Width = 15;
                }


                // 特别强调第一行的顶部边框,不然标题边框没有
                for (int col = 1; col <= columnCount; col++)
                {
                    // 使用最粗的边框样式
                    worksheet.Cells[1, col].Style.Border.Top.Style = ExcelBorderStyle.Thick;
                    worksheet.Cells[1, col].Style.Border.Left.Style = ExcelBorderStyle.Thick;
                    worksheet.Cells[1, col].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                }

                // 设置冻结窗格
                //worksheet.View.FreezePanes(4, 1);
                #region 设置冻结窗格后，背景线超出范围需要清除
                for (int row = 11; row <= 20; row++)
                {
                    for (int col = 1; col <= columnCount + 5; col++)
                    {
                        worksheet.Cells[row, col].Style.Fill.PatternType = ExcelFillStyle.None;
                        worksheet.Cells[row, col].Style.Border.Top.Style = ExcelBorderStyle.None;
                        worksheet.Cells[row, col].Style.Border.Left.Style = ExcelBorderStyle.None;
                        worksheet.Cells[row, col].Style.Border.Right.Style = ExcelBorderStyle.None;
                        worksheet.Cells[row, col].Style.Border.Bottom.Style = ExcelBorderStyle.None;
                    }
                }
                // 清除超出列范围的单元格样式
                for (int row = 1; row <= 10; row++)
                {
                    for (int col = columnCount + 1; col <= columnCount + 5; col++)
                    {
                        worksheet.Cells[row, col].Style.Fill.PatternType = ExcelFillStyle.None;
                        worksheet.Cells[row, col].Style.Border.Top.Style = ExcelBorderStyle.None;
                        worksheet.Cells[row, col].Style.Border.Left.Style = ExcelBorderStyle.None;
                        worksheet.Cells[row, col].Style.Border.Right.Style = ExcelBorderStyle.None;
                        worksheet.Cells[row, col].Style.Border.Bottom.Style = ExcelBorderStyle.None;
                    }
                }
                #endregion
                package.Save();
            }
            return new DownLoadByStreamResult(stream, $"患者随访记录表{System.DateTime.Now.ToString("yyyy-MM-dd")}.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }


        // 用于解析JSON数据的类
        public class FollowupDataModel
        {
            public List<FollowupVariable> variables { get; set; } = null!;
        }

        public class FollowupVariable
        {
            public string variable_name { get; set; } = string.Empty;
            public string 变量描述 { get; set; } = string.Empty;
            public string value { get; set; } = string.Empty;
        }

        public class Variable
        {
            public string variable_name { get; set; }
            public string value { get; set; }
            public string source { get; set; }
        }

        public class Root
        {
            public List<Variable> variables { get; set; }
        }

    }
}
